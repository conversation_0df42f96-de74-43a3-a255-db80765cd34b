# RCoT - 递归思维链系统

RCoT（Recursive Chain of Thought System）是一个基于大型语言模型的系统框架，通过递归分解、明确的依赖跟踪、持久化状态管理和适应性原则来处理复杂任务。

## 项目组件

本项目包含以下核心组件：

### 1. SuperCoder 开发助手
位于 `SuperCoder/` 目录，由 **Marcus Chen博士** 设计的高级AI编程助手系统。具备五模式执行引擎（研究→创新→规划→执行→审查）、专业角色激活和MCP工具链，实现智能化的全栈开发自动化。

- 核心协议：`SuperCoder/SuperCoder_Protocol.md`

### 2. 智能助理系统
位于 `AssistantRCoT/` 目录，由 **小静** 提供的智能办公助力框架，包含OCR工具等实用模块。

- 核心规则：`AssistantRCoT/SmartAssistant.md`
- 工具模块：`AssistantRCoT/tools/`

### 3. 专业角色协议集

#### 企业管理咨询
- **股权专家**：`EnterpriseManagementConsultingRCoT/EquityExpert.md` - 陈志远教授设计的股权咨询协议

#### 其他专业角色
- **需求分析专家**：`OtherPrompts/RequirementAnalyst.md` - 林悦博士设计的需求分析协议
- **提示工程架构师**：`OtherPrompts/PromptArchitect.md` - Alex Chen设计的提示词工程协议

### 4. 通用规则
- `General_Rules.md`：项目通用行为规则

## 环境要求

部分工具模块可能需要Python环境和相关依赖，具体要求请查看相应模块的文档。

## 许可证

本项目采用自定义许可证，详情请查看 [LICENSE](./LICENSE) 文件。

**重要提示**：未经版权所有者明确授权，禁止使用、复制或分发本软件。未授权使用可能导致法律诉讼。

## 联系方式

如需授权或其他问题，请与项目维护者联系。

## 使用说明

### SuperCoder 部署

1. **克隆仓库**
   ```bash
   git clone <repository_url> RCTS
   cd RCTS
   ```

2. **集成协议**
   将 `SuperCoder/SuperCoder_Protocol.md` 的完整内容复制到IDE的全局规则配置中：
   - 打开IDE设置 → 找到"Rules"或"System Prompt"配置
   - 粘贴完整的SuperCoder协议内容
   - 确保五模式执行引擎和所有工具集成规则生效

3. **开始使用**
   直接向AI助手描述开发需求，系统将自动激活相应的执行模式。

### 其他角色协议使用

选择所需的专业角色协议文件，将其内容集成到AI系统的规则配置中即可使用相应的专业能力。

## 语言版本

- [中文](./README.md)
- [English](./README.en-US.md) 