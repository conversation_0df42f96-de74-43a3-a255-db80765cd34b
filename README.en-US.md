# RCoT - Recursive Chain of Thought System

RCoT (Recursive Chain of Thought System) is a framework based on large language models designed to handle complex tasks through recursive decomposition, explicit dependency tracking, persistent state management, and adaptability principles.

## Project Components

This project contains the following core components:

### 1. SuperCoder Development Assistant
Located in the `SuperCoder/` directory, this advanced AI programming assistant system is designed by **<PERSON><PERSON>. It features a five-mode execution engine (Research→Innovation→Planning→Execution→Review), professional role activation, and MCP toolchain to achieve intelligent full-stack development automation.

- Core Protocol: `SuperCoder/SuperCoder_Protocol.md`

### 2. Smart Assistant System
Located in the `AssistantRCoT/` directory, this intelligent office assistance framework is provided by **<PERSON> Jing**, including practical modules like OCR tools.

- Core Rules: `AssistantRCoT/SmartAssistant.md`
- Tool Modules: `AssistantRCoT/tools/`

### 3. Professional Role Protocol Suite

#### Enterprise Management Consulting
- **Equity Expert**: `EnterpriseManagementConsultingRCoT/EquityExpert.md` - Equity consulting protocol designed by Professor <PERSON>

#### Other Professional Roles
- **Requirements Analyst**: `OtherPrompts/RequirementAnalyst.md` - Requirements analysis protocol designed by <PERSON><PERSON>
- **Prompt Architect**: `OtherPrompts/PromptArchitect.md` - Prompt engineering protocol designed by <PERSON> <PERSON>

### 4. General Rules
- `General_Rules.md`: Project-wide behavioral rules

## Environment Requirements

Some tool modules may require Python environment and related dependencies. Please refer to the respective module documentation for specific requirements.

## License

This project uses a custom license. Please see the [LICENSE](./LICENSE) file for details.

**Important Notice**: Use and copying of this software are permitted under conditions specified in the license. Unauthorized distribution or commercial use is strictly prohibited. Proper attribution to the original author and project link is required for all usage. Unauthorized use may result in legal action.

## Contact

For authorization or other inquiries, please contact the project maintainer.

## Usage Instructions

### SuperCoder Deployment

1. **Clone the Repository**
   ```bash
   git clone <repository_url> RCTS
   cd RCTS
   ```

2. **Integrate Protocol**
   Copy the complete content of `SuperCoder/SuperCoder_Protocol.md` to your IDE's global rules configuration:
   - Open IDE settings → Find "Rules" or "System Prompt" configuration
   - Paste the complete SuperCoder protocol content
   - Ensure the five-mode execution engine and all tool integration rules take effect

3. **Start Using**
   Simply describe your development needs to the AI assistant, and the system will automatically activate the appropriate execution mode.

### Other Role Protocol Usage

Select the desired professional role protocol file and integrate its content into the AI system's rule configuration to use the corresponding professional capabilities.

## Language Versions

- [中文](./README.md)
- [English](./README.en-US.md) 